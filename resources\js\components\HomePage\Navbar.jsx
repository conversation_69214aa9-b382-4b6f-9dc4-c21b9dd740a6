import clsx from "clsx";
import gsap from "gsap";
import { useWindowScroll } from "react-use";
import { useEffect, useRef, useState } from "react";
import { TiLocationArrow } from "react-icons/ti";

import Button from "./Button";

const navItems = ["Nexus", "Vault", "Prologue", "About", "Contact"];

const NavBar = () => {
  // State for toggling audio and visual indicator
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [isIndicatorActive, setIsIndicatorActive] = useState(false);

  // State for mobile menu
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Refs for audio and navigation container
  const audioElementRef = useRef(null);
  const navContainerRef = useRef(null);
  const mobileMenuRef = useRef(null);
  const hamburgerRef = useRef(null);
  const menuItemsRef = useRef([]);

  const { y: currentScrollY } = useWindowScroll();
  const [isNavVisible, setIsNavVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // Toggle audio and visual indicator
  const toggleAudioIndicator = () => {
    setIsAudioPlaying((prev) => !prev);
    setIsIndicatorActive((prev) => !prev);
  };

  // Toggle mobile menu with animations
  const toggleMobileMenu = () => {
    if (!isMobileMenuOpen) {
      // Open menu
      setIsMobileMenuOpen(true);
      document.body.style.overflow = 'hidden'; // Lock body scroll

      // Animate hamburger to X
      gsap.to(hamburgerRef.current.children[0], {
        rotation: 45,
        y: 8,
        duration: 0.3,
        ease: "power2.out"
      });
      gsap.to(hamburgerRef.current.children[1], {
        opacity: 0,
        duration: 0.2,
        ease: "power2.out"
      });
      gsap.to(hamburgerRef.current.children[2], {
        rotation: -45,
        y: -8,
        duration: 0.3,
        ease: "power2.out"
      });

      // Animate overlay
      gsap.fromTo(mobileMenuRef.current,
        { y: "-100%", opacity: 0 },
        { y: "0%", opacity: 1, duration: 0.5, ease: "power2.out" }
      );

      // Staggered animation for menu items
      gsap.fromTo(menuItemsRef.current,
        { y: 50, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          delay: 0.2,
          ease: "power2.out"
        }
      );
    } else {
      // Close menu
      document.body.style.overflow = 'unset'; // Unlock body scroll

      // Animate X back to hamburger
      gsap.to(hamburgerRef.current.children[0], {
        rotation: 0,
        y: 0,
        duration: 0.3,
        ease: "power2.out"
      });
      gsap.to(hamburgerRef.current.children[1], {
        opacity: 1,
        duration: 0.2,
        delay: 0.1,
        ease: "power2.out"
      });
      gsap.to(hamburgerRef.current.children[2], {
        rotation: 0,
        y: 0,
        duration: 0.3,
        ease: "power2.out"
      });

      // Animate overlay out
      gsap.to(mobileMenuRef.current, {
        y: "-100%",
        opacity: 0,
        duration: 0.4,
        ease: "power2.out",
        onComplete: () => setIsMobileMenuOpen(false)
      });
    }
  };

  // Manage audio playback
  useEffect(() => {
    if (isAudioPlaying) {
      audioElementRef.current.play();
    } else {
      audioElementRef.current.pause();
    }
  }, [isAudioPlaying]);

  useEffect(() => {
    if (currentScrollY === 0) {
      // Topmost position: show navbar without floating-nav
      setIsNavVisible(true);
      navContainerRef.current.classList.remove("floating-nav");
    } else if (currentScrollY > lastScrollY) {
      // Scrolling down: hide navbar and apply floating-nav
      setIsNavVisible(false);
      navContainerRef.current.classList.add("floating-nav");
    } else if (currentScrollY < lastScrollY) {
      // Scrolling up: show navbar with floating-nav
      setIsNavVisible(true);
      navContainerRef.current.classList.add("floating-nav");
    }

    setLastScrollY(currentScrollY);
  }, [currentScrollY, lastScrollY]);

  useEffect(() => {
    gsap.to(navContainerRef.current, {
      y: isNavVisible ? 0 : -100,
      opacity: isNavVisible ? 1 : 0,
      duration: 0.2,
    });
  }, [isNavVisible]);

  return (
    <div
      ref={navContainerRef}
      className="fixed inset-x-0 top-4 z-50 h-16 border-none transition-all duration-700 sm:inset-x-6"
    >
      <header className="absolute top-1/2 w-full -translate-y-1/2">
        <nav className="flex size-full items-center justify-between p-4">
          {/* Logo and Product button */}
          <div className="flex items-center gap-7">
            <img src="/img/logo.png" alt="logo" className="w-10" />

            <Button
              id="product-button"
              title="Products"
              rightIcon={<TiLocationArrow />}
              containerClass="bg-blue-50 md:flex hidden items-center justify-center gap-1"
            />
          </div>

          {/* Navigation Links and Audio Button */}
          <div className="flex h-full items-center">
            {/* Desktop Navigation */}
            <div className="hidden md:block">
              {navItems.map((item, index) => (
                <a
                  key={index}
                  href={`#${item.toLowerCase()}`}
                  className="nav-hover-btn"
                >
                  {item}
                </a>
              ))}
            </div>

            {/* Audio Button */}
            <button
              onClick={toggleAudioIndicator}
              className="ml-10 flex items-center space-x-0.5"
            >
              <audio
                ref={audioElementRef}
                className="hidden"
                src="/audio/loop.mp3"
                loop
              />
              {[1, 2, 3, 4].map((bar) => (
                <div
                  key={bar}
                  className={clsx("indicator-line", {
                    active: isIndicatorActive,
                  })}
                  style={{
                    animationDelay: `${bar * 0.1}s`,
                  }}
                />
              ))}
            </button>

            {/* Hamburger Menu Button - Mobile Only */}
            <button
              ref={hamburgerRef}
              onClick={toggleMobileMenu}
              className="ml-4 md:hidden flex flex-col justify-center items-center w-8 h-8 space-y-1.5 z-[70] relative"
            >
              <div className="w-6 h-0.5 bg-white hamburger-line"></div>
              <div className="w-6 h-0.5 bg-white hamburger-line"></div>
              <div className="w-6 h-0.5 bg-white hamburger-line"></div>
            </button>
          </div>
        </nav>
      </header>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          ref={mobileMenuRef}
          className="fixed inset-0 z-[60] bg-black bg-opacity-95 flex flex-col items-center justify-center md:hidden mobile-menu-overlay"
        >
          {/* Mobile Navigation Items */}
          <div className="flex flex-col items-center space-y-12">
            {navItems.map((item, index) => (
              <a
                key={index}
                ref={(el) => (menuItemsRef.current[index] = el)}
                href={`#${item.toLowerCase()}`}
                onClick={toggleMobileMenu}
                className="text-white text-5xl font-bold uppercase tracking-widest mobile-menu-item"
              >
                {item}
              </a>
            ))}
          </div>

          {/* Mobile Audio Button */}
          <div className="mt-16">
            <button
              onClick={toggleAudioIndicator}
              className="flex items-center space-x-0.5"
            >
              {[1, 2, 3, 4].map((bar) => (
                <div
                  key={bar}
                  className={clsx("indicator-line", {
                    active: isIndicatorActive,
                  })}
                  style={{
                    animationDelay: `${bar * 0.1}s`,
                  }}
                />
              ))}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NavBar;